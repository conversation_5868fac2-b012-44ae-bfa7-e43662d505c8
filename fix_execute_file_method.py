#!/usr/bin/env python3
"""
Script to fix the _execute_file method indentation
"""

from pathlib import Path

def fix_execute_file_method():
    """Fix the _execute_file method indentation"""
    
    agent_file = Path("agent.py")
    if not agent_file.exists():
        print("agent.py not found!")
        return False
    
    # Read the file
    with open(agent_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the _execute_file method and fix its indentation
    fixed_lines = []
    in_execute_file_method = False
    method_start_line = None
    
    for i, line in enumerate(lines):
        if line.strip().startswith('def _execute_file(self, args: str) -> str:'):
            in_execute_file_method = True
            method_start_line = i
            fixed_lines.append(line)  # Method definition is already correct
            continue
        
        if in_execute_file_method:
            # Check if we've reached the next method or end of class
            if (line.strip().startswith('def ') and 
                not line.strip().startswith('def _execute_file') and
                line.startswith('    def ')):
                # We've reached the next method
                in_execute_file_method = False
                fixed_lines.append(line)
                continue
            
            # Fix indentation for method content
            if line.strip():  # Non-empty line
                if not line.startswith('        '):  # Should be 8 spaces for method content
                    # Add proper indentation
                    fixed_line = '        ' + line.lstrip()
                    print(f"Fixed line {i+1}: {line.strip()}")
                    fixed_lines.append(fixed_line)
                else:
                    fixed_lines.append(line)
            else:
                # Empty line, keep as is
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    # Write the fixed content back
    with open(agent_file, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed _execute_file method indentation")
    return True

if __name__ == "__main__":
    success = fix_execute_file_method()
    if success:
        print("✓ Successfully fixed _execute_file method")
    else:
        print("✗ Failed to fix _execute_file method")
